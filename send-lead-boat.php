<?php
session_start(); // Start the session to store data
header('Content-Type: application/json'); // Set JSON response header

// Log request for debugging
$logFile = 'boat_form_debug.log';
file_put_contents($logFile, date('Y-m-d H:i:s') . " - Request received\n", FILE_APPEND);
file_put_contents($logFile, date('Y-m-d H:i:s') . " - Request method: " . $_SERVER['REQUEST_METHOD'] . "\n", FILE_APPEND);

// Handle direct GET requests to this file
if ($_SERVER['REQUEST_METHOD'] === 'GET') {
    echo json_encode([
        'success' => false,
        'message' => 'This endpoint requires a POST request with form data',
        'redirect' => 'boat-shipping'
    ]);
    exit;
}

// Check if we have form data
if (!empty($_POST)) {
    file_put_contents($logFile, date('Y-m-d H:i:s') . " - Processing POST data\n", FILE_APPEND);
    file_put_contents($logFile, date('Y-m-d H:i:s') . " - POST data: " . print_r($_POST, true) . "\n", FILE_APPEND);
    
    $authKey = "23d66938-96a5-4ad4-b1b5-122c12a307ea"; // Sandbox Key

    // Collect form data
    $name = !empty($_POST['name']) ? $_POST['name'] : "unknown";
    $email = !empty($_POST['email']) ? $_POST['email'] : "unknown";
    $phone = !empty($_POST['phone']) ? $_POST['phone'] : "unknown";
    $origin = !empty($_POST['transport_from']) ? $_POST['transport_from'] : "unknown";
    $destination = !empty($_POST['transport_to']) ? $_POST['transport_to'] : "unknown";
    $vehicle_brand = !empty($_POST['vehicle_brand']) ? $_POST['vehicle_brand'] : "unknown";
    $vehicle_model = !empty($_POST['vehicle_model']) ? $_POST['vehicle_model'] : "unknown";
    $vehicle_year = !empty($_POST['vehicle_year']) ? (int)$_POST['vehicle_year'] : "unknown";
    $available_date = !empty($_POST['available_date']) ? $_POST['available_date'] : "asap";
    $vehicle_type = "Boat"; // Set vehicle type to Boat
    
    // Check if boat is on trailer
    $transport_type = isset($_POST['transport_type']) && $_POST['transport_type'] === 'Yes' ? 1 : 2;
    
    // Use the exact date from the datepicker
    $ship_date = $available_date;

    // If for some reason the date is not in the correct format, default to today
    if (!preg_match('/^\d{2}\/\d{2}\/\d{4}$/', $ship_date)) {
        $ship_date = date("m/d/Y"); // Default to today
    }
    
    // Process location data
    // Extract city, state, zip from origin and destination
    $origin_parts = explode(',', $origin);
    $destination_parts = explode(',', $destination);
    
    // Default values
    $origin_city = "unknown";
    $origin_state = "unknown";
    $origin_postal_code = "unknown";
    $destination_city = "unknown";
    $destination_state = "unknown";
    $destination_postal_code = "unknown";
    
    // Process origin
    if (count($origin_parts) >= 2) {
        $origin_city = trim($origin_parts[0]);
        $origin_state_zip = explode(' ', trim($origin_parts[1]));
        $origin_state = $origin_state_zip[0];
        $origin_postal_code = isset($origin_state_zip[1]) ? $origin_state_zip[1] : "unknown";
    } elseif (count($origin_parts) == 1) {
        // Check if it's a ZIP code
        if (is_numeric(trim($origin_parts[0]))) {
            $origin_postal_code = trim($origin_parts[0]);
        } else {
            $origin_city = trim($origin_parts[0]);
        }
    }
    
    // Process destination
    if (count($destination_parts) >= 2) {
        $destination_city = trim($destination_parts[0]);
        $destination_state_zip = explode(' ', trim($destination_parts[1]));
        $destination_state = $destination_state_zip[0];
        $destination_postal_code = isset($destination_state_zip[1]) ? $destination_state_zip[1] : "unknown";
    } elseif (count($destination_parts) == 1) {
        // Check if it's a ZIP code
        if (is_numeric(trim($destination_parts[0]))) {
            $destination_postal_code = trim($destination_parts[0]);
        } else {
            $destination_city = trim($destination_parts[0]);
        }
    }
    
    file_put_contents($logFile, date('Y-m-d H:i:s') . " - Processed locations\n", FILE_APPEND);
    file_put_contents($logFile, date('Y-m-d H:i:s') . " - Origin: $origin_city, $origin_state, $origin_postal_code\n", FILE_APPEND);
    file_put_contents($logFile, date('Y-m-d H:i:s') . " - Destination: $destination_city, $destination_state, $destination_postal_code\n", FILE_APPEND);

    $vehicles = [
        [
            "vehicle_inop" => 0, // Boats are typically operable
            "vehicle_make" => $vehicle_brand,
            "vehicle_model" => $vehicle_model,
            "vehicle_model_year" => $vehicle_year,
            "vehicle_type" => "Boat" // Use "Boat" instead of numeric code
        ]
    ];

    $data = [
        "AuthKey" => $authKey,
        "first_name" => $name,
        "last_name" => "",
        "email" => $email,
        "phone" => preg_replace('/[^0-9]/', '', $phone),
        "comment_from_shipper" => "Boat Transport",
        "origin_city" => $origin_city,
        "origin_state" => $origin_state,
        "origin_postal_code" => $origin_postal_code,
        "origin_country" => "US",
        "destination_city" => $destination_city,
        "destination_state" => $destination_state,
        "destination_postal_code" => $destination_postal_code,
        "destination_country" => "US",
        "ship_date" => $ship_date,
        "transport_type" => $transport_type,
        "Vehicles" => $vehicles
    ];
    
    file_put_contents($logFile, date('Y-m-d H:i:s') . " - Prepared API data\n", FILE_APPEND);
    file_put_contents($logFile, date('Y-m-d H:i:s') . " - API data: " . json_encode($data, JSON_PRETTY_PRINT) . "\n", FILE_APPEND);

    // Store data in session for thank-you.php
    $_SESSION['name'] = $name;
    $_SESSION['email'] = $email;
    $_SESSION['phone'] = $phone;
    $_SESSION['transport_from'] = $origin;
    $_SESSION['transport_to'] = $destination;
    $_SESSION['vehicle_brand'] = $vehicle_brand;
    $_SESSION['vehicle_model'] = $vehicle_model;
    $_SESSION['vehicle_year'] = $vehicle_year;
    $_SESSION['transport_type'] = $transport_type === 1 ? 'Open' : 'Enclosed';
    $_SESSION['vehicle_type'] = $vehicle_type;
    $_SESSION['ship_date'] = $ship_date;
    
    file_put_contents($logFile, date('Y-m-d H:i:s') . " - Stored data in session\n", FILE_APPEND);

    // Send API Request
    $ch = curl_init("https://api.batscrm.com/leads");
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    
    curl_setopt($ch, CURLOPT_HTTPHEADER, [
        'Content-Type: application/json',
        'Accept: application/json',
        'Authorization: Bearer ' . $authKey
    ]);
    curl_setopt($ch, CURLOPT_POST, true);
    curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, true);
    curl_setopt($ch, CURLOPT_TIMEOUT, 30);
    
    file_put_contents($logFile, date('Y-m-d H:i:s') . " - Sending API request\n", FILE_APPEND);

    $result = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    
    file_put_contents($logFile, date('Y-m-d H:i:s') . " - API response code: $httpCode\n", FILE_APPEND);
    file_put_contents($logFile, date('Y-m-d H:i:s') . " - API response: $result\n", FILE_APPEND);
    
    // Debugging - log the full API response
    error_log("BATS CRM API Response for Boat: " . $result);
    
    curl_close($ch);

    if ($httpCode === 200) {
        // Try to parse the response to get lead_id
        $lead_id = null;
        $responseData = json_decode($result, true);
        
        if (json_last_error() === JSON_ERROR_NONE && isset($responseData['lead_id'])) {
            $lead_id = $responseData['lead_id'];
        } elseif (preg_match('/OK, Lead :(\d+)/', $result, $matches)) {
            $lead_id = $matches[1];
        } else {
            $lead_id = 'TEMP-' . uniqid();
        }

        $_SESSION['lead_id'] = $lead_id;
        file_put_contents($logFile, date('Y-m-d H:i:s') . " - Success! Lead ID: $lead_id\n", FILE_APPEND);
        
        // Redirect to thank-you.php on success
        echo json_encode([
            'success' => true,
            'message' => 'Order sent successfully!',
            'lead_id' => $lead_id,
            'redirect' => 'thank-you'
        ]);
    } else {
        // On error, generate a temporary ID but store the error
        $_SESSION['lead_id'] = 'ERR-' . uniqid();
        $_SESSION['api_error'] = "HTTP $httpCode: " . $result;
        error_log("BATS CRM API Error for Boat: HTTP $httpCode - " . $result);
        
        file_put_contents($logFile, date('Y-m-d H:i:s') . " - Error! Generated temp ID: " . $_SESSION['lead_id'] . "\n", FILE_APPEND);
        
        // Return error response
        echo json_encode([
            'success' => false,
            'message' => 'Error sending order. Please try again.',
            'error' => "HTTP $httpCode: " . $result
        ]);
    }
    exit;
} else {
    // If no data in request, return error
    file_put_contents($logFile, date('Y-m-d H:i:s') . " - No data in request. Method: " . $_SERVER['REQUEST_METHOD'] . "\n", FILE_APPEND);
    file_put_contents($logFile, date('Y-m-d H:i:s') . " - Request data: " . print_r($_REQUEST, true) . "\n", FILE_APPEND);
    
    echo json_encode([
        'success' => false,
        'message' => 'Invalid request: No form data received'
    ]);
    exit;
}
?>

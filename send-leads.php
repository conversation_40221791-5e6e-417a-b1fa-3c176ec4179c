<?php
session_start(); // Start the session to store data

$response = "";

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $authKey = "23d66938-96a5-4ad4-b1b5-122c12a307ea"; // Sandbox Key

    // Collect form data
    $name = !empty($_POST['name']) ? $_POST['name'] : "unknown";
    $email = !empty($_POST['email']) ? $_POST['email'] : "unknown";
    $phone = !empty($_POST['phone']) ? $_POST['phone'] : "unknown";
    $origin = !empty($_POST['transport_from']) ? $_POST['transport_from'] : "unknown";
    $destination = !empty($_POST['transport_to']) ? $_POST['transport_to'] : "unknown";
    $vehicle_brand = !empty($_POST['vehicle_brand']) ? $_POST['vehicle_brand'] : "unknown";
    $vehicle_model = !empty($_POST['vehicle_model']) ? $_POST['vehicle_model'] : "unknown";
    $vehicle_year = !empty($_POST['vehicle_year']) ? (int)$_POST['vehicle_year'] : "unknown";
    $vehicle_operable = !empty($_POST['vehicle_operable']) ? $_POST['vehicle_operable'] : "unknown";
    $available_date = !empty($_POST['available_date']) ? $_POST['available_date'] : "asap";
    $vehicle_type = !empty($_POST['vehicle_type']) ? $_POST['vehicle_type'] : "Car";

    // transport_type & vehicle_inop
    $transport_type = ($_POST['transport_type'] === 'Open') ? 1 : 2;
    $vehicle_inop = ($vehicle_operable === 'no') ? 1 : 0;

    // Use the exact date from the datepicker
    $ship_date = $available_date;

    // If for some reason the date is not in the correct format, default to today
    if (!preg_match('/^\d{2}\/\d{2}\/\d{4}$/', $ship_date)) {
        $ship_date = date("m/d/Y"); // Default to today
    }

    // Default ZIP codes for each state (same as in map.js)
    $defaultStateZips = [
        'AL' => '36104', 'AK' => '99801', 'AZ' => '85001', 'AR' => '72201', 'CA' => '94203',
        'CO' => '80202', 'CT' => '06103', 'DE' => '19901', 'FL' => '32301', 'GA' => '30303',
        'HI' => '96813', 'ID' => '83702', 'IL' => '62701', 'IN' => '46204', 'IA' => '50309',
        'KS' => '66603', 'KY' => '40601', 'LA' => '70802', 'ME' => '04330', 'MD' => '21401',
        'MA' => '02201', 'MI' => '48933', 'MN' => '55102', 'MS' => '39205', 'MO' => '65101',
        'MT' => '59623', 'NE' => '68502', 'NV' => '89701', 'NH' => '03301', 'NJ' => '08608',
        'NM' => '87501', 'NY' => '12207', 'NC' => '27601', 'ND' => '58501', 'OH' => '43215',
        'OK' => '73102', 'OR' => '97301', 'PA' => '17101', 'RI' => '02903', 'SC' => '29217',
        'SD' => '57501', 'TN' => '37219', 'TX' => '78701', 'UT' => '84111', 'VT' => '05602',
        'VA' => '23219', 'WA' => '98507', 'WV' => '25301', 'WI' => '53703', 'WY' => '82001',
        'DC' => '20001'
    ];

    // Process origin location
    $origin_parts = explode(",", $origin);
    $origin_city = trim($origin_parts[0]); // City

    // Initialize with defaults
    $origin_state = "unknown";
    $origin_postal_code = "unknown";

    // Check if we have state/zip information
    if (isset($origin_parts[1])) {
        $origin_state_zip = trim($origin_parts[1]);

        // If origin_state_zip contains both state and zip (e.g., "CA 90210")
        if (preg_match('/([A-Za-z]{2})\s*(\d{5})/', $origin_state_zip, $matches)) {
            $origin_state = $matches[1];
            $origin_postal_code = $matches[2];
        }
        // If it's just a state code (e.g., "CA")
        elseif (preg_match('/^[A-Za-z]{2}$/', $origin_state_zip)) {
            $origin_state = $origin_state_zip;
            // Use default ZIP for this state
            $origin_postal_code = isset($defaultStateZips[$origin_state]) ? $defaultStateZips[$origin_state] : "unknown";
        }
        // If it's just a zip code
        elseif (is_numeric($origin_state_zip) || preg_match('/^\d{5}$/', $origin_state_zip)) {
            $origin_postal_code = $origin_state_zip;
            // Try to determine state from USA part if available
            if (isset($origin_parts[2]) && preg_match('/^[A-Za-z]{2}/', trim($origin_parts[2]), $stateMatch)) {
                $origin_state = $stateMatch[0];
            }
        }
        // If it contains a state code with spaces (e.g., "New York")
        else {
            $stateParts = explode(' ', $origin_state_zip);
            foreach ($stateParts as $part) {
                if (isset($defaultStateZips[strtoupper($part)])) {
                    $origin_state = strtoupper($part);
                    $origin_postal_code = $defaultStateZips[$origin_state];
                    break;
                }
            }
        }
    }

    // Process destination location
    $destination_parts = explode(",", $destination);
    $destination_city = trim($destination_parts[0]); // City

    // Initialize with defaults
    $destination_state = "unknown";
    $destination_postal_code = "unknown";

    // Check if we have state/zip information
    if (isset($destination_parts[1])) {
        $destination_state_zip = trim($destination_parts[1]);

        // If destination_state_zip contains both state and zip (e.g., "CA 90210")
        if (preg_match('/([A-Za-z]{2})\s*(\d{5})/', $destination_state_zip, $matches)) {
            $destination_state = $matches[1];
            $destination_postal_code = $matches[2];
        }
        // If it's just a state code (e.g., "CA")
        elseif (preg_match('/^[A-Za-z]{2}$/', $destination_state_zip)) {
            $destination_state = $destination_state_zip;
            // Use default ZIP for this state
            $destination_postal_code = isset($defaultStateZips[$destination_state]) ? $defaultStateZips[$destination_state] : "unknown";
        }
        // If it's just a zip code
        elseif (is_numeric($destination_state_zip) || preg_match('/^\d{5}$/', $destination_state_zip)) {
            $destination_postal_code = $destination_state_zip;
            // Try to determine state from USA part if available
            if (isset($destination_parts[2]) && preg_match('/^[A-Za-z]{2}/', trim($destination_parts[2]), $stateMatch)) {
                $destination_state = $stateMatch[0];
            }
        }
        // If it contains a state code with spaces (e.g., "New York")
        else {
            $stateParts = explode(' ', $destination_state_zip);
            foreach ($stateParts as $part) {
                if (isset($defaultStateZips[strtoupper($part)])) {
                    $destination_state = strtoupper($part);
                    $destination_postal_code = $defaultStateZips[$destination_state];
                    break;
                }
            }
        }
    }

    $vehicles = [
        [
            "vehicle_inop" => $vehicle_inop,
            "vehicle_make" => $vehicle_brand,
            "vehicle_model" => $vehicle_model,
            "vehicle_model_year" => $vehicle_year,
            "vehicle_type" => $vehicle_type
        ]
    ];

    $data = [
        "AuthKey" => $authKey,
        "first_name" => $name,
        "last_name" => "",
        "email" => $email,
        "phone" => $phone,
        "comment_from_shipper" => "unknown",
        "origin_city" => $origin_city,
        "origin_state" => $origin_state, // State for origin
        "origin_postal_code" => $origin_postal_code, // ZIP for origin
        "origin_country" => "US",
        "destination_city" => $destination_city,
        "destination_state" => $destination_state, // State for destination
        "destination_postal_code" => $destination_postal_code, // ZIP for destination
        "destination_country" => "US",
        "ship_date" => $ship_date, // Now using the calculated date based on selection
        "transport_type" => $transport_type,
        "Vehicles" => $vehicles
    ];

    $json = json_encode($data);

    // Store data in session for checkout.php
    $_SESSION['name'] = $name;
    $_SESSION['email'] = $email;
    $_SESSION['phone'] = $phone;
    $_SESSION['origin_city'] = $origin_city;
    $_SESSION['origin_state'] = $origin_state;
    $_SESSION['origin_postal_code'] = $origin_postal_code;
    $_SESSION['destination_city'] = $destination_city;
    $_SESSION['destination_state'] = $destination_state;
    $_SESSION['destination_postal_code'] = $destination_postal_code;
    $_SESSION['vehicle_brand'] = $vehicle_brand;
    $_SESSION['vehicle_model'] = $vehicle_model;
    $_SESSION['vehicle_year'] = $vehicle_year;
    $_SESSION['transport_type'] = $transport_type;
    $_SESSION['vehicle_inop'] = $vehicle_inop;
    $_SESSION['vehicle_type'] = $vehicle_type;
    $_SESSION['vehicle_types'] = $vehicle_type; // Use vehicle_type for vehicle_types as well
    $_SESSION['ship_date'] = $ship_date; // Store the calculated ship date

    // Send API Request
    $ch = curl_init("https://api.batscrm.com/leads");
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    
    curl_setopt($ch, CURLOPT_HTTPHEADER, [
        'Content-Type: application/json'
    ]);
    curl_setopt($ch, CURLOPT_POST, true);
    curl_setopt($ch, CURLOPT_POSTFIELDS, $json);


      $result = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    
    // Debugging - log the full API response
    error_log("BATS CRM API Response: " . $result);
    error_log("BATS CRM API HTTP Code: " . $httpCode);

    // Additional debugging - write to a debug file
    $debug_info = [
        'timestamp' => date('Y-m-d H:i:s'),
        'http_code' => $httpCode,
        'raw_response' => $result,
        'request_data' => $json
    ];
    file_put_contents('debug_bats_api.log', json_encode($debug_info, JSON_PRETTY_PRINT) . "\n\n", FILE_APPEND);
    
    curl_close($ch);

    if ($httpCode === 200) {
        // Try to parse the response in different ways
        $lead_id = null;
        
        // First try JSON decode
        $responseData = json_decode($result, true);
        if (json_last_error() === JSON_ERROR_NONE) {
            $lead_id = $responseData['lead_id'] ?? 
                      $responseData['data']['lead_id'] ?? 
                      $responseData['id'] ?? null;
        }
        
        // If JSON decode failed or didn't contain lead_id, try text parsing
        if (!$lead_id) {
            // Check for "OK, Lead :21046214" format
            if (preg_match('/OK, Lead :(\d+)/', $result, $matches)) {
                $lead_id = $matches[1];
            }
            // Check for other possible text formats
            elseif (preg_match('/Lead(?: ID)?[: ]*(\d+)/i', $result, $matches)) {
                $lead_id = $matches[1];
            }
        }
        
        // If we still don't have an ID, create a temporary one
        if (!$lead_id) {
            $lead_id = 'TEMP-' . uniqid();
            error_log("Could not extract lead_id from API response: " . $result . ". Using temporary ID: " . $lead_id);
            file_put_contents('debug_bats_api.log', date('Y-m-d H:i:s') . " - Could not extract lead_id from response: " . $result . "\n", FILE_APPEND);
        }

        // Store all important data in session
        $_SESSION['lead_id'] = $lead_id;
        $_SESSION['api_response_raw'] = $result; // Store raw response for debugging

        header("Location: checkout.php");
        exit;
    } else {
        // On error, generate a temporary ID but store the error
        $_SESSION['lead_id'] = 'ERR-' . uniqid();
        $_SESSION['api_error'] = "HTTP $httpCode: " . $result;
        error_log("BATS CRM API Error: HTTP $httpCode - " . $result);
        
        header("Location: checkout.php");
        exit;
    }    
    
}
?>
